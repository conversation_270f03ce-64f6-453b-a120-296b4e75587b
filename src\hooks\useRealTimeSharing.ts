'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { RealTimeUser, RealTimeMessage } from '@/types/boss';

interface PeerConnection {
  id: string;
  connection: RTCPeerConnection;
  dataChannel?: RTCDataChannel;
  user?: RealTimeUser;
}

const ICE_SERVERS = [
  { urls: 'stun:stun.l.google.com:19302' },
  { urls: 'stun:stun1.l.google.com:19302' },
];

const USER_COLORS = [
  '#3B82F6', '#EF4444', '#10B981', '#F59E0B', 
  '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'
];

export function useRealTimeSharing() {
  const [isConnected, setIsConnected] = useState(false);
  const [isHost, setIsHost] = useState(false);
  const [roomId, setRoomId] = useState<string | null>(null);
  const [currentUser, setCurrentUser] = useState<RealTimeUser | null>(null);
  const [connectedUsers, setConnectedUsers] = useState<RealTimeUser[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const [error, setError] = useState<string | null>(null);

  const peersRef = useRef<Map<string, PeerConnection>>(new Map());
  const messageHandlersRef = useRef<((message: RealTimeMessage) => void)[]>([]);
  const isInitializedRef = useRef(false);

  // Generate a simple room ID
  const generateRoomId = useCallback(() => {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }, []);

  // Generate user ID and color
  const generateUser = useCallback((name: string): RealTimeUser => {
    const id = Math.random().toString(36).substring(2, 15);
    const color = USER_COLORS[Math.floor(Math.random() * USER_COLORS.length)];
    return {
      id,
      name,
      color,
      lastSeen: new Date(),
    };
  }, []);



  // Send message to all connected peers
  const broadcastMessage = useCallback((message: RealTimeMessage) => {
    const messageStr = JSON.stringify(message);
    peersRef.current.forEach((peer) => {
      if (peer.dataChannel && peer.dataChannel.readyState === 'open') {
        try {
          peer.dataChannel.send(messageStr);
        } catch (error) {
          console.error('Failed to send message to peer:', error);
        }
      }
    });
  }, []);



  // Add message handler
  const addMessageHandler = useCallback((handler: (message: RealTimeMessage) => void) => {
    messageHandlersRef.current.push(handler);
    return () => {
      messageHandlersRef.current = messageHandlersRef.current.filter(h => h !== handler);
    };
  }, []);

  // Create a new room (host)
  const createRoom = useCallback(async (userName: string): Promise<string> => {
    try {
      setConnectionStatus('connecting');
      setError(null);

      const newRoomId = generateRoomId();
      const user = generateUser(userName);
      
      setRoomId(newRoomId);
      setCurrentUser(user);
      setIsHost(true);
      setConnectedUsers([user]);
      setIsConnected(true);
      setConnectionStatus('connected');

      // Store room info in localStorage for recovery
      localStorage.setItem('realtime-room', JSON.stringify({
        roomId: newRoomId,
        user,
        isHost: true,
      }));

      return newRoomId;
    } catch (error) {
      setError('Failed to create room');
      setConnectionStatus('disconnected');
      throw error;
    }
  }, [generateRoomId, generateUser]);

  // Join an existing room
  const joinRoom = useCallback(async (roomId: string, userName: string): Promise<void> => {
    try {
      setConnectionStatus('connecting');
      setError(null);

      const user = generateUser(userName);
      setRoomId(roomId);
      setCurrentUser(user);
      setIsHost(false);

      // Store room info in localStorage for recovery
      localStorage.setItem('realtime-room', JSON.stringify({
        roomId,
        user,
        isHost: false,
      }));

      // For now, we'll implement a simple signaling mechanism using localStorage
      // In a real implementation, you'd use a signaling server
      setConnectedUsers([user]);
      setIsConnected(true);
      setConnectionStatus('connected');

    } catch (error) {
      setError('Failed to join room');
      setConnectionStatus('disconnected');
      throw error;
    }
  }, [generateUser]);

  // Leave the current room
  const leaveRoom = useCallback(() => {
    // Close all peer connections
    peersRef.current.forEach((peer) => {
      if (peer.dataChannel) {
        peer.dataChannel.close();
      }
      peer.connection.close();
    });
    peersRef.current.clear();

    // Send leave message
    if (currentUser) {
      const leaveMessage: RealTimeMessage = {
        type: 'user_leave',
        userId: currentUser.id,
        userName: currentUser.name,
        timestamp: new Date(),
        data: {},
      };
      broadcastMessage(leaveMessage);
    }

    // Reset state
    setIsConnected(false);
    setIsHost(false);
    setRoomId(null);
    setCurrentUser(null);
    setConnectedUsers([]);
    setConnectionStatus('disconnected');
    setError(null);

    // Clear localStorage
    localStorage.removeItem('realtime-room');
  }, [currentUser, broadcastMessage]);

  // Send a message to all connected peers
  const sendMessage = useCallback((type: RealTimeMessage['type'], data: unknown) => {
    if (!currentUser) return;

    const message: RealTimeMessage = {
      type,
      userId: currentUser.id,
      userName: currentUser.name,
      timestamp: new Date(),
      data,
    };

    broadcastMessage(message);
  }, [currentUser, broadcastMessage]);

  // Initialize from localStorage on mount
  useEffect(() => {
    if (isInitializedRef.current) return;
    isInitializedRef.current = true;

    const saved = localStorage.getItem('realtime-room');
    if (saved) {
      try {
        const { roomId: savedRoomId, user, isHost: savedIsHost } = JSON.parse(saved);
        setRoomId(savedRoomId);
        setCurrentUser(user);
        setIsHost(savedIsHost);
        setConnectedUsers([user]);
        setIsConnected(true);
        setConnectionStatus('connected');
      } catch (error) {
        console.error('Failed to restore room from localStorage:', error);
        localStorage.removeItem('realtime-room');
      }
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      const peers = peersRef.current;
      peers.forEach((peer) => {
        if (peer.dataChannel) {
          peer.dataChannel.close();
        }
        peer.connection.close();
      });
    };
  }, []);

  return {
    // State
    isConnected,
    isHost,
    roomId,
    currentUser,
    connectedUsers,
    connectionStatus,
    error,

    // Actions
    createRoom,
    joinRoom,
    leaveRoom,
    sendMessage,
    addMessageHandler,
  };
}
