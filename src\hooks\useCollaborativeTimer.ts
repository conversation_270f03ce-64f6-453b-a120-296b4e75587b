'use client';

import { useState, useEffect, useCallback } from 'react';
import { TimerState, Boss, RealTimeMessage, TimerAction, BossAction } from '@/types/boss';
import { useNotifications } from './useNotifications';
import { useRealTimeSharing } from './useRealTimeSharing';

const STORAGE_KEY = 'boss-timer-state';

export function useCollaborativeTimer() {
  const [timerState, setTimerState] = useState<TimerState>({});
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isClient, setIsClient] = useState(false);
  const [lastAction, setLastAction] = useState<{ userId: string; userName: string; action: string; timestamp: Date } | null>(null);
  
  const { settings: notificationSettings, showNotification, playNotificationSound, stopNotificationSound, isPlaying } = useNotifications();
  const { isConnected, currentUser, sendMessage, addMessageHandler } = useRealTimeSharing();

  // Set client flag after mount
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Load state from localStorage on mount (client-side only)
  useEffect(() => {
    if (!isClient) return;

    const saved = localStorage.getItem(STORAGE_KEY);
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        // Convert date strings back to Date objects
        const converted: TimerState = {};
        Object.keys(parsed).forEach(key => {
          converted[key] = {
            ...parsed[key],
            lastKilled: parsed[key].lastKilled ? new Date(parsed[key].lastKilled) : undefined,
            nextRespawn: parsed[key].nextRespawn ? new Date(parsed[key].nextRespawn) : undefined,
          };
        });
        setTimerState(converted);
      } catch (error) {
        console.error('Failed to parse saved timer state:', error);
      }
    }
  }, [isClient]);

  // Save state to localStorage whenever it changes (client-side only)
  useEffect(() => {
    if (!isClient) return;
    localStorage.setItem(STORAGE_KEY, JSON.stringify(timerState));
  }, [timerState, isClient]);

  // Handle real-time messages
  useEffect(() => {
    if (!isConnected) return;

    const handleRealTimeMessage = (message: RealTimeMessage) => {
      switch (message.type) {
        case 'timer_start': {
          const { bossId, killTime, respawnHours } = message.data as TimerAction;
          const killDate = new Date(killTime || message.timestamp);
          const nextRespawn = new Date(killDate.getTime() + respawnHours * 60 * 60 * 1000);
          
          setTimerState(prev => ({
            ...prev,
            [bossId]: {
              lastKilled: killDate,
              nextRespawn,
              isActive: true,
              notificationSent: false,
            },
          }));

          setLastAction({
            userId: message.userId,
            userName: message.userName,
            action: `started timer for ${bossId}`,
            timestamp: new Date(message.timestamp),
          });
          break;
        }

        case 'timer_stop': {
          const { bossId } = message.data as TimerAction;
          setTimerState(prev => ({
            ...prev,
            [bossId]: {
              ...prev[bossId],
              isActive: false,
            },
          }));

          setLastAction({
            userId: message.userId,
            userName: message.userName,
            action: `stopped timer for ${bossId}`,
            timestamp: new Date(message.timestamp),
          });
          break;
        }

        case 'timer_reset': {
          const { bossId } = message.data as TimerAction;
          setTimerState(prev => {
            const newState = { ...prev };
            delete newState[bossId];
            return newState;
          });

          setLastAction({
            userId: message.userId,
            userName: message.userName,
            action: `reset timer for ${bossId}`,
            timestamp: new Date(message.timestamp),
          });
          break;
        }

        case 'sync_request': {
          // Send current timer state to requesting user
          if (currentUser) {
            sendMessage('sync_response', { timerState });
          }
          break;
        }

        case 'sync_response': {
          // Merge received timer state (only if we don't have data or theirs is newer)
          const { timerState: receivedState } = message.data;
          setTimerState(prev => {
            const merged = { ...prev };
            Object.entries(receivedState).forEach(([bossId, state]: [string, any]) => {
              const existingState = merged[bossId];
              const receivedTimestamp = new Date(state.lastKilled || 0);
              const existingTimestamp = new Date(existingState?.lastKilled || 0);
              
              // Use the newer state
              if (!existingState || receivedTimestamp > existingTimestamp) {
                merged[bossId] = {
                  ...state,
                  lastKilled: state.lastKilled ? new Date(state.lastKilled) : undefined,
                  nextRespawn: state.nextRespawn ? new Date(state.nextRespawn) : undefined,
                };
              }
            });
            return merged;
          });
          break;
        }
      }
    };

    return addMessageHandler(handleRealTimeMessage);
  }, [isConnected, currentUser, sendMessage, addMessageHandler]);

  // Request sync when connecting to a room
  useEffect(() => {
    if (isConnected && currentUser) {
      // Request sync from other users
      sendMessage('sync_request', {});
    }
  }, [isConnected, currentUser, sendMessage]);

  // Update current time every second and check for notifications
  const checkNotifications = useCallback((now: Date) => {
    Object.entries(timerState).forEach(([bossId, state]) => {
      if (!state.isActive || !state.nextRespawn) return;

      const timeUntilRespawn = state.nextRespawn.getTime() - now.getTime();
      const minutesUntilRespawn = Math.floor(timeUntilRespawn / (1000 * 60));

      // Check if boss has already respawned
      if (timeUntilRespawn <= 0 && !state.notificationSent) {
        showNotification(`Boss Respawned!`, {
          body: `${bossId} has respawned and is ready to hunt!`,
          tag: `boss-respawned-${bossId}`,
        });
        playNotificationSound();

        // Mark notification as sent
        setTimerState(prev => ({
          ...prev,
          [bossId]: {
            ...prev[bossId],
            notificationSent: true,
          },
        }));
        return;
      }

      // Check warning notifications
      notificationSettings.warningMinutes.forEach(warningMinutes => {
        if (minutesUntilRespawn === warningMinutes && !state.notificationSent) {
          showNotification(`Boss Respawn Warning`, {
            body: `${bossId} will respawn in ${warningMinutes} minutes!`,
            tag: `boss-warning-${bossId}-${warningMinutes}`,
          });
          playNotificationSound();
        }
      });
    });
  }, [timerState, notificationSettings.warningMinutes, showNotification, playNotificationSound]);

  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      setCurrentTime(now);

      // Check for notification triggers
      if (notificationSettings.enabled) {
        checkNotifications(now);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [notificationSettings.enabled, checkNotifications]);

  const startTimer = useCallback((bossId: string, respawnHours: number, customKillTime?: Date) => {
    const killTime = customKillTime || new Date();
    const nextRespawn = new Date(killTime.getTime() + respawnHours * 60 * 60 * 1000);

    setTimerState(prev => ({
      ...prev,
      [bossId]: {
        lastKilled: killTime,
        nextRespawn,
        isActive: true,
        notificationSent: false,
      },
    }));

    // Send real-time update
    if (isConnected) {
      sendMessage('timer_start', {
        bossId,
        killTime,
        respawnHours,
      });
    }
  }, [isConnected, sendMessage]);

  const stopTimer = useCallback((bossId: string) => {
    setTimerState(prev => ({
      ...prev,
      [bossId]: {
        ...prev[bossId],
        isActive: false,
      },
    }));

    // Send real-time update
    if (isConnected) {
      sendMessage('timer_stop', { bossId });
    }
  }, [isConnected, sendMessage]);

  const resetTimer = useCallback((bossId: string) => {
    setTimerState(prev => {
      const newState = { ...prev };
      delete newState[bossId];
      return newState;
    });

    // Send real-time update
    if (isConnected) {
      sendMessage('timer_reset', { bossId });
    }
  }, [isConnected, sendMessage]);

  const getTimeRemaining = useCallback((bossId: string): string | null => {
    const state = timerState[bossId];
    if (!state?.isActive || !state.nextRespawn) return null;

    const now = currentTime.getTime();
    const respawnTime = state.nextRespawn.getTime();
    const timeLeft = respawnTime - now;

    if (timeLeft <= 0) return 'Respawned!';

    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  }, [timerState, currentTime]);

  const isRespawned = useCallback((bossId: string): boolean => {
    const state = timerState[bossId];
    if (!state?.isActive || !state.nextRespawn) return false;
    return state.nextRespawn.getTime() <= currentTime.getTime();
  }, [timerState, currentTime]);

  return {
    timerState,
    currentTime,
    lastAction,
    startTimer,
    stopTimer,
    resetTimer,
    getTimeRemaining,
    isRespawned,
    stopNotificationSound,
    isPlaying,
  };
}
